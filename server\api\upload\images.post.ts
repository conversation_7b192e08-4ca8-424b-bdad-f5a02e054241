/**
 * 图片上传接口
 * POST /api/upload/images
 */

export default defineApiHandler(async event => {
  try {
    // 检查用户是否已登录
    const userId = event.context.user?.id
    if (!userId) {
      throw new AuthenticationError('请先登录')
    }

    // 解析表单数据
    const form = await readMultipartFormData(event)
    if (!form || form.length === 0) {
      throw new ValidationError('请选择要上传的图片')
    }

    // 使用默认的图片上传配置
    const uploadedFiles = await uploadFiles(form, defaultUploadConfigs.images)

    if (uploadedFiles.length === 0) {
      throw new ValidationError('没有有效的图片文件')
    }

    return createSuccessResponse(
      {
        files: uploadedFiles,
        count: uploadedFiles.length
      },
      '图片上传成功'
    )
  } catch (error) {
    console.error('图片上传失败:', error)

    if (error instanceof ApiError) {
      throw error
    }

    throw new InternalServerError('图片上传失败')
  }
})
